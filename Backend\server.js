const express = require("express");
const path = require("path");
const fs = require("fs");
const https = require("https");
const dotenv = require("dotenv");
const cors = require("cors");
const helmet = require("helmet");
const morgan = require("morgan");
const connectDB = require("./config/db");
const errorHandler = require("./middleware/errorHandler");
const { initializeStorage } = require("./utils/storageHelper");
const jobScheduler = require("./jobs/scheduler");
const createDefaultAdmin = require("./utils/createDefaultAdmin");

// Load environment variables
dotenv.config();

// Connect to database
console.log("MongoDB URI:", process.env.MONGODB_URI);
connectDB();

// Create default admin user
createDefaultAdmin();

// Initialize storage system
initializeStorage();

// Start background job scheduler
console.log("🚀 Initializing background job scheduler...");
try {
  jobScheduler.start();
  console.log("✅ Background job scheduler started successfully");
} catch (error) {
  console.error("❌ Failed to start background job scheduler:", error);
}

// Start URL refresh service
console.log("🔄 Initializing URL refresh service...");
try {
  const urlRefreshService = require("./services/urlRefreshService");
  urlRefreshService.start();
  console.log("✅ URL refresh service started successfully");
} catch (error) {
  console.error("❌ Failed to start URL refresh service:", error);
}

// Initialize Express app
const app = express();

// Body parser with increased limits for large file uploads
// Note: For file uploads, multer handles the parsing, so these limits are for JSON/form data only
app.use(express.json({ limit: "1gb" }));
app.use(express.urlencoded({ extended: true, limit: "1gb" }));

// Security middleware
app.use(
  helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        frameAncestors: [
          "'self'",
          "http://localhost:5173",
          "http://localhost:3000",
          "http://127.0.0.1:5173",
          "http://127.0.0.1:3000",
          "https://localhost:5173",
          "https://localhost:3000",
          "https://xosportshub.thefabaf.com",
          "https://xosports.thefabaf.com",
          "http://xosportshub.thefabaf.com",
          "http://xosports.thefabaf.com",
          "https://xosportshub-dev.thefabaf.com",
          "http://xosportshub-dev.thefabaf.com",
          "https://xosportshub.com",
          "http://xosportshub.com",
          "https://api.xosportshub.com",
          "http://api.xosportshub.com",
          "https://www.xosportshub.com",
          "http://www.xosportshub.com",
        ],
      },
    },
    crossOriginResourcePolicy: { policy: "cross-origin" },
    crossOriginOpenerPolicy: { policy: "same-origin-allow-popups" },
  })
);
// CORS configuration
const corsOptions = {
  origin: [
    "http://localhost:3000",
    "http://localhost:5173",
    "http://127.0.0.1:3000",
    "http://127.0.0.1:5173",
    "https://localhost:3000",
    "https://localhost:5173",
    "https://xosportshub.thefabaf.com",
    "https://xosports.thefabaf.com",
    "http://xosportshub.thefabaf.com",
    "http://xosports.thefabaf.com",
    "https://xosportshub-dev.thefabaf.com",
    "http://xosportshub-dev.thefabaf.com",
    "https://xosportshub.com",
    "http://xosportshub.com",
    "https://api.xosportshub.com",
    "http://api.xosportshub.com",
    "https://www.xosportshub.com",
    "http://www.xosportshub.com",
    process.env.FRONTEND_URL,
  ].filter(Boolean),
  credentials: true,
  methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
  allowedHeaders: [
    "Content-Type",
    "Authorization",
    "X-Requested-With",
    "Accept",
    "Origin",
  ],
};

app.use(cors(corsOptions));

// Handle preflight requests for large uploads
app.options("*", (req, res) => {
  const origin = req.headers.origin;
  const allowedOrigins = [
    "http://localhost:3000",
    "http://localhost:5173",
    "http://127.0.0.1:3000",
    "http://127.0.0.1:5173",
    "https://localhost:3000",
    "https://localhost:5173",
    "https://xosportshub.thefabaf.com",
    "https://xosports.thefabaf.com",
    "http://xosportshub.thefabaf.com",
    "http://xosports.thefabaf.com",
    "https://xosportshub-dev.thefabaf.com",
    "http://xosportshub-dev.thefabaf.com",
    "https://xosportshub.com",
    "http://xosportshub.com",
    "https://api.xosportshub.com",
    "http://api.xosportshub.com",
    "https://www.xosportshub.com",
    "http://www.xosportshub.com",
    process.env.FRONTEND_URL,
  ].filter(Boolean);

  if (allowedOrigins.includes(origin)) {
    res.header("Access-Control-Allow-Origin", origin);
  }
  res.header("Access-Control-Allow-Methods", "GET,PUT,POST,DELETE,PATCH,OPTIONS");
  res.header(
    "Access-Control-Allow-Headers",
    "Content-Type, Authorization, Content-Length, X-Requested-With, Accept, Origin"
  );
  res.header("Access-Control-Allow-Credentials", "true");
  res.header("Access-Control-Max-Age", "86400"); // 24 hours
  res.sendStatus(200);
});

// Logging middleware
if (process.env.NODE_ENV === "development") {
  app.use(morgan("dev"));
}

// Serve static files from "uploads" folder with CORP headers
app.use(
  "/uploads",
  express.static(path.join(__dirname, "uploads"), {
    setHeaders: (res, filePath) => {
      res.setHeader("Cross-Origin-Resource-Policy", "cross-origin"); // Allow usage in cross-origin iframes/images
      res.setHeader("Access-Control-Allow-Origin", "*"); // Optional: Allow any domain (for images only, not sensitive data)
    },
  })
);

// Launch status middleware (add headers for all API routes)
const { addLaunchStatusHeaders, handleLaunchStatusError } = require('./middleware/launchStatus');
app.use('/api', addLaunchStatusHeaders);

// Define routes
app.use("/api/auth", require("./routes/auth"));
app.use("/api/users", require("./routes/users"));
app.use("/api/content", require("./routes/content"));
app.use("/api/orders", require("./routes/orders"));
app.use("/api/bids", require("./routes/bids"));
app.use("/api/offers", require("./routes/offers"));
app.use("/api/requests", require("./routes/requests"));
app.use("/api/payments", require("./routes/payments"));
app.use("/api/cards", require("./routes/cards"));
app.use("/api/notifications", require("./routes/notifications"));
app.use("/api/cms", require("./routes/cms"));
app.use("/api/settings", require("./routes/settings"));
app.use("/api/dashboard", require("./routes/dashboard"));
app.use("/api/reviews", require("./routes/reviews"));
app.use("/api/messages", require("./routes/messages"));
// app.use("/api/document-preview", require("./routes/documentPreview"));

// Diagnostics routes (development and testing)
if (process.env.NODE_ENV !== "production") {
  app.use("/api/diagnostics", require("./routes/diagnostics"));
  app.use("/api/jobs", require("./routes/jobs"));
}
app.use("/api/wishlist", require("./routes/wishlist"));

// Admin-specific routes
app.use('/api/admin/dashboard', require('./routes/admin/dashboard'));
app.use('/api/admin/users', require('./routes/admin/users'));
app.use('/api/admin/content', require('./routes/admin/content'));
app.use('/api/admin/bids', require('./routes/admin/bids'));
app.use('/api/admin/cms', require('./routes/admin/cms'));
app.use('/api/admin/orders', require('./routes/admin/orders'));
app.use('/api/admin/payments', require('./routes/admin/payments'));
app.use('/api/admin/offers', require('./routes/admin/offers'));
app.use('/api/admin/requests', require('./routes/admin/requests'));
app.use('/api/admin/reviews', require('./routes/admin/reviews'));
app.use('/api/admin/notifications', require('./routes/admin/notifications'));
app.use('/api/admin/settings', require('./routes/admin/settings'));

// Health check route
app.get("/health", (req, res) => {
  res.status(200).json({ status: "ok" });
});

// Launch status error handler
app.use(handleLaunchStatusError);

// Error handling middleware
app.use(errorHandler);

// Server configuration based on environment
let server;
const PORT = process.env.PORT || 5000;
const isLocal =
  process.env.NODE_ENV === "development" && process.env.USE_HTTPS !== "true";

if (isLocal) {
  // Use HTTP for local development
  server = app.listen(PORT, () => {
    console.log(
      `HTTP Server running in ${process.env.NODE_ENV} mode on port ${PORT}`
    );
    console.log(`Server URL: http://localhost:${PORT}`);
  });

  // Set timeout for long uploads (1 hour for large video files)
  server.timeout = 3600000; // 1 hour (3600 seconds)
  server.keepAliveTimeout = 65000; // 65 seconds
  server.headersTimeout = 66000; // 66 seconds
} else {
  // Use HTTPS for development/production environments
  try {
    // Read SSL certificate files
    const privateKey = fs.readFileSync(
      path.join(__dirname, "secret", "thefabaf-private.key"),
      "utf8"
    );
    const certificate = fs.readFileSync(
      path.join(__dirname, "secret", "STAR_thefabaf_com.cer"),
      "utf8"
    );
    const ca = fs.readFileSync(
      path.join(__dirname, "secret", "My_CA_Bundle.ca-bundle"),
      "utf8"
    );

    // SSL credentials
    const credentials = {
      key: privateKey,
      cert: certificate,
      ca: ca,
    };

    // Create HTTPS server
    server = https.createServer(credentials, app);

    server.listen(PORT, () => {
      console.log(
        `HTTPS Server running in ${process.env.NODE_ENV} mode on port ${PORT}`
      );
      console.log(`Server URL: https://localhost:${PORT}`);
    });

    // Set timeout for long uploads (1 hour for large video files)
    server.timeout = 3600000; // 1 hour (3600 seconds)
    server.keepAliveTimeout = 65000; // 65 seconds
    server.headersTimeout = 66000; // 66 seconds
  } catch (error) {
    console.warn(
      "SSL certificates not found or invalid, falling back to HTTP server"
    );
    console.warn("Error:", error.message);

    // Fallback to HTTP server if SSL certificates are not available
    server = app.listen(PORT, () => {
      console.log(
        `HTTP Server (fallback) running in ${process.env.NODE_ENV} mode on port ${PORT}`
      );
      console.log(`Server URL: http://localhost:${PORT}`);
    });

    // Set timeout for long uploads (1 hour for large video files)
    server.timeout = 3600000; // 1 hour (3600 seconds)
    server.keepAliveTimeout = 65000; // 65 seconds
    server.headersTimeout = 66000; // 66 seconds
  }
}

// Handle graceful shutdown
const gracefulShutdown = (signal) => {
  console.log(`\n${signal} received. Starting graceful shutdown...`);

  // Stop background job scheduler
  try {
    jobScheduler.stop();
    console.log("✅ Background job scheduler stopped");
  } catch (error) {
    console.error("❌ Error stopping job scheduler:", error);
  }

  // Close server
  server.close(() => {
    console.log("✅ Server closed");
    process.exit(0);
  });
};

// Handle shutdown signals
process.on("SIGTERM", () => gracefulShutdown("SIGTERM"));
process.on("SIGINT", () => gracefulShutdown("SIGINT"));

// Handle unhandled promise rejections
process.on("unhandledRejection", (err, promise) => {
  console.log(`Error: ${err.message}`);
  // Close server & exit process
  server.close(() => process.exit(1));
});
